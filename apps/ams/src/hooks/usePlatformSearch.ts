import { useState, useMemo, useCallback } from 'react'
import { IPlatform } from '@dtbx/store/interfaces'

interface UsePlatformSearchProps {
  platforms: IPlatform[]
  itemsPerPage?: number
}

interface UsePlatformSearchReturn {
  filteredPlatforms: IPlatform[]
  displayedPlatforms: IPlatform[]
  searchQuery: string
  currentPage: number
  totalPages: number
  hasMore: boolean
  setSearchQuery: (query: string) => void
  loadMore: () => void
  resetPagination: () => void
}

export const usePlatformSearch = ({
  platforms,
  itemsPerPage = 10,
}: UsePlatformSearchProps): UsePlatformSearchReturn => {
  const [searchQuery, setSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)

  const filteredPlatforms = useMemo(() => {
    if (!searchQuery.trim()) {
      return platforms
    }

    return platforms.filter((platform) =>
      platform.name.toLowerCase().includes(searchQuery.toLowerCase())
    )
  }, [platforms, searchQuery])

  const totalPages = Math.ceil(filteredPlatforms.length / itemsPerPage)
  const hasMore = currentPage < totalPages

  const displayedPlatforms = useMemo(() => {
    return filteredPlatforms.slice(0, currentPage * itemsPerPage)
  }, [filteredPlatforms, currentPage, itemsPerPage])

  const loadMore = useCallback(() => {
    if (hasMore) {
      setCurrentPage((prev) => prev + 1)
    }
  }, [hasMore])

  const resetPagination = useCallback(() => {
    setCurrentPage(1)
  }, [])

  const handleSetSearchQuery = useCallback((query: string) => {
    setSearchQuery(query)
    setCurrentPage(1)
  }, [])

  return {
    filteredPlatforms,
    displayedPlatforms,
    searchQuery,
    currentPage,
    totalPages,
    hasMore,
    setSearchQuery: handleSetSearchQuery,
    loadMore,
    resetPagination,
  }
}

// Hook for future backend pagination support
interface UseBackendPaginationProps {
  fetchFunction: (page: number, size: number, search?: string) => Promise<void>
  itemsPerPage?: number
}

interface UseBackendPaginationReturn {
  currentPage: number
  searchQuery: string
  isLoading: boolean
  setSearchQuery: (query: string) => void
  loadMore: () => void
  refresh: () => void
}

export const useBackendPagination = ({
  fetchFunction,
  itemsPerPage = 10,
}: UseBackendPaginationProps): UseBackendPaginationReturn => {
  const [currentPage, setCurrentPage] = useState(1)
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const loadMore = useCallback(async () => {
    if (isLoading) return

    setIsLoading(true)
    try {
      await fetchFunction(currentPage + 1, itemsPerPage, searchQuery)
      setCurrentPage((prev) => prev + 1)
    } catch (error) {
      console.error('Error loading more data:', error)
    } finally {
      setIsLoading(false)
    }
  }, [currentPage, itemsPerPage, searchQuery, fetchFunction, isLoading])

  const refresh = useCallback(async () => {
    if (isLoading) return

    setIsLoading(true)
    setCurrentPage(1)
    try {
      await fetchFunction(1, itemsPerPage, searchQuery)
    } catch (error) {
      console.error('Error refreshing data:', error)
    } finally {
      setIsLoading(false)
    }
  }, [itemsPerPage, searchQuery, fetchFunction, isLoading])

  const handleSetSearchQuery = useCallback(
    (query: string) => {
      setSearchQuery(query)
      refresh()
    },
    [refresh]
  )

  return {
    currentPage,
    searchQuery,
    isLoading,
    setSearchQuery: handleSetSearchQuery,
    loadMore,
    refresh,
  }
}
