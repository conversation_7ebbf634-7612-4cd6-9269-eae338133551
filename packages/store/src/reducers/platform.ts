import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { IPlatform, IPlatformSummary } from '../interfaces'

export interface IPlatformState {
  platforms: IPlatform[]
  isLoadingPlatforms: boolean
  platformsSummary: IPlatformSummary
}

const initialState: IPlatformState = {
  platforms: [],
  isLoadingPlatforms: false,
  platformsSummary: {
    pageNumber: 0,
    pageSize: 0,
    totalNumberOfPages: 0,
    totalElements: 0,
  },
}

const platformSlice = createSlice({
  name: 'platform',
  initialState,
  reducers: {
    setPlatforms: (state, action: PayloadAction<IPlatform[]>) => {
      state.platforms = action.payload
    },
    setIsLoadingPlatforms: (state, action: PayloadAction<boolean>) => {
      state.isLoadingPlatforms = action.payload
    },
    setPlatformsSummary: (state, action: PayloadAction<IPlatformSummary>) => {
      state.platformsSummary = action.payload
    },
  },
})

export const {
  setPlatforms,
  setIsLoadingPlatforms,
  setPlatformsSummary,
} = platformSlice.actions

export default platformSlice.reducer
