import { Dispatch } from '@reduxjs/toolkit';
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime';

declare const refreshToken: () => Promise<void>;
type TokenObject = {
    accessToken: string;
    refreshToken: string;
    success: boolean;
};
declare const handleLogin: (tokenObject: TokenObject, dispatch: Dispatch, router: AppRouterInstance) => Promise<void>;
declare const fetchChannelModules: (dispatch: Dispatch) => Promise<void>;

declare const getUserPlatform: (dispatch: Dispatch, params?: string) => Promise<any>;

export { fetchChannelModules, getUserPlatform, handleLogin, refreshToken };
