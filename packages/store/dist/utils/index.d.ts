import React, { ReactNode } from 'react';
import * as axios from 'axios';
import { AxiosInstance } from 'axios';

interface IAccessControlWrapperProps {
    rights: string[];
    modules?: string[];
    children: ReactNode;
    makerId?: string;
    isMake?: boolean;
}
/**
 * This function gets the access token from localstorage and decodes it to get the roles assigned to the user and the modules they have access to
 * The function accepts an array of roles and an array of modules and returns a boolean for whether the user has access to the module or not
 */
declare const AccessControlHandler: (rights: string[], makerId?: string, isMake?: boolean) => boolean;
declare const HasAccessToRights: (rights: string[]) => boolean;
declare const checkIsMaker: (makerId?: string) => boolean;
declare const AccessControlWrapper: React.FC<IAccessControlWrapperProps>;

/**
 * Checks if the user is logged in
 */
declare const isLoggedIn: () => boolean;
declare const clearStore: () => void;

interface IApprovalRequest {
    checker?: string;
    checkerComments?: string;
    id: string;
    maker: string;
    dateCreated: string;
    dateModified: string;
    makerCheckerType: {
        channel: string;
        checkerPermissions: string[];
        description?: string;
        makerPermissions: string[];
        module: string;
        name: string;
        overridePermissions: string[];
        type: string;
    };
    entityId?: string;
    entity?: string;
    diff: IDiffValues[];
    makerComments?: string;
    status: string;
}
interface IDiffValues {
    field: string;
    name?: string;
    oldValue: IDiffValues[] | string;
    newValue: IDiffValues[] | string;
}
/**
 * Gets the initials of a full name eg Jane Doe => JD
 * @param name
 */
declare const getInitials: (name?: string) => string;
declare const rightsFormatter: (str: string) => string;
declare function getTimeOfDay(): {
    timeOfDay: string;
    formattedTime: string;
};
declare function formatCurrency(value: string | number | undefined, currency?: string, locale?: string): string;
declare function formatTimestamp(timestamp: string): string;
declare const formatTimeOnly: (dateString: string) => string;
declare const formatDate: (dateString: string) => string;
/**
 * Calculates the week number of the year for a given date.
 *
 * @param {Date} [date=new Date()] - The date for which to calculate the week number. Defaults to the current date if no date is provided.
 * @return {number} The week number of the year corresponding to the given date.
 */
declare function getWeekOfYear(date?: Date): number;
declare function getAuctionWeek(): number;
/**
 *
 * @param date
 * @param inputFormat Date format
 * @param outputFormat Output format
 */
declare const formatCustomDate: (date: string, inputFormat?: string, outputFormat?: string) => string;
declare const formatDateTime: (dateString: string) => string;
declare const isObjEmpty: (obj: object) => boolean;
interface FormValues {
    [key: string]: string | number | boolean | FormValues | FormValues[];
}
declare const trimSpace: <T extends FormValues>(obj: T) => T;
declare const generateMarks: (step: number, start: number, end: number) => {
    value: number;
    label: string;
}[];
declare const downloadBlob: (blob: Blob, filename: string) => void;
declare const handleDiff: (diff?: IDiffValues[]) => string;
/** This is used to extract info from approval requests**/
declare const extractFields: (field: string, row: IApprovalRequest) => string | IDiffValues[] | undefined;
/** get base64 from a file **/
declare function getBase64(file: File): Promise<unknown>;
declare const isAccountLinkingApprovalRequest: (entity: string) => boolean;
declare const formatText: (text: string) => string;
declare const addUnderscores: (text: string) => string;
declare const formatCamelCaseToWords: (text: string) => string;

declare const ACCESS_CONTROLS: {
    REJECT_APPROVALREQUEST_CUSTOMERS: string[];
    ACCEPT_APPROVALREQUEST_CUSTOMERS: string[];
    CREATE_CUSTOMERS: string[];
    UPDATE_CUSTOMERS: string[];
    DEACTIVATE_CUSTOMERS: string[];
    ACTIVATE_CUSTOMERS: string[];
    DELETE_CUSTOMERS: string[];
    CREATE_DEVICE: string[];
    ACTIVATE_DEVICE: string[];
    DEACTIVATE_DEVICE: string[];
    ACCEPT_APPROVAL_REQUEST_DEVICES: string[];
    REJECT_APPROVAL_REQUEST_DEVICES: string[];
    RESET_SECURITY_QUESTIONS: string[];
    ACCEPT_RESET_SECURITY_QUESTIONS: string[];
    REJECT_RESET_SECURITY_QUESTIONS: string[];
    CREATE_USERS: string[];
    UPDATE_USERS: string[];
    DEACTIVATE_ACTIVATE_USERS: string[];
    ACCEPT_APPROVALREQUEST_USERS: string[];
    REJECT_APPROVALREQUEST_USERS: string[];
    CREATE_ROLES: string[];
    UPDATE_ROLES: string[];
    DELETE_ROLE: string[];
    REJECT_APPROVALREQUEST_ROLES: string[];
    ACCEPT_APPROVALREQUEST_ROLES: string[];
    CREATE_NOTIFICATIONS: string[];
    UPDATE_NOTIFICATIONS: string[];
    DELETE_NOTIFICATIONS: string[];
    ACCEPT_NOTIFICATIONS: string[];
    REJECT_NOTIFICATIONS: string[];
    UPDATE_ACCOUNT_PREFERENCES: string[];
    REJECT_ACCOUNT_PREFERENCES: string[];
    ACCEPT_ACCOUNT_PREFERENCES: string[];
    ACCEPT_APPROVALREQUEST_PROFILES: string[];
    REJECT_APPROVALREQUEST_PROFILES: string[];
    ACTIVATE_CARDS: string[];
    REJECT_APPROVALREQUEST_CARDS: string[];
    ACCEPT_APPROVALREQUEST_CARDS: string[];
    RESET_PIN_TRY_COUNTER: string[];
    BRANCH_ACTIVATE_CARDS: string[];
    BRANCH_ACCEPT_ACTIVATE_CARDS: string[];
    BRANCH_REJECT_ACTIVATE_CARDS: string[];
    BRANCH_VIEW_CARDS: string[];
    CREATE_TARIFF: string[];
    UPDATE_SERVICE_CONFIGS: string[];
    CREATE_SERVICE_CONFIGS: string[];
    REJECT_TARIFFS: string[];
    ACCEPT_TARIFFS: string[];
};
declare const restrictReasons: string[];
declare const reasonsForUnlinking: string[];
declare const reasonsForActivation: string[];
declare const reasonsForDeleting: string[];
declare const reasonsForDeactivating: string[];
declare const reasonsForUnsubscribing: string[];
declare const alertTypes: {
    label: string;
    type: string;
    frequency: string;
}[];

declare const authConfig: {
    baseURL: string | undefined;
    headers: {
        'Content-type': string;
        Accept: string;
    };
};
declare const authConfig2: {
    baseURL: string | undefined;
    headers: {
        'Content-type': string;
        Accept: string;
    };
};

declare const openapi: AxiosInstance;
declare const openapi2: AxiosInstance;

declare function refreshTokenIfNeeded(refreshToken: () => Promise<void>): Promise<string | null>;
declare const secureapi: axios.AxiosInstance;
declare const secureapi2: axios.AxiosInstance;

export { ACCESS_CONTROLS, AccessControlHandler, AccessControlWrapper, type FormValues, HasAccessToRights, type IAccessControlWrapperProps, type IApprovalRequest, type IDiffValues, addUnderscores, alertTypes, authConfig, authConfig2, checkIsMaker, clearStore, downloadBlob, extractFields, formatCamelCaseToWords, formatCurrency, formatCustomDate, formatDate, formatDateTime, formatText, formatTimeOnly, formatTimestamp, generateMarks, getAuctionWeek, getBase64, getInitials, getTimeOfDay, getWeekOfYear, handleDiff, isAccountLinkingApprovalRequest, isLoggedIn, isObjEmpty, openapi, openapi2, reasonsForActivation, reasonsForDeactivating, reasonsForDeleting, reasonsForUnlinking, reasonsForUnsubscribing, refreshTokenIfNeeded, restrictReasons, rightsFormatter, secureapi, secureapi2, trimSpace };
