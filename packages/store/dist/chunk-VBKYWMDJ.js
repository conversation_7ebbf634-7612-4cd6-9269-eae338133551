// src/reducers/auth.ts
import { createSlice } from "@reduxjs/toolkit";
var initialState = {
  isLoadingLogin: false,
  isLoginSuccess: false,
  loginErrorMessage: "",
  isLoginError: false,
  decodedToken: {},
  channelModules: [],
  // login actions
  userInfo: typeof window !== "undefined" && localStorage.getItem("userInfo") ? JSON.parse(localStorage.getItem("userInfo") || "{}") : null
};
var authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setIsLoadingLogin: (state, action) => {
      state.isLoadingLogin = action.payload;
    },
    setIsLoginSuccess: (state, action) => {
      state.isLoginSuccess = action.payload;
    },
    setIsLoginError: (state, action) => {
      state.isLoginError = action.payload;
    },
    setDecodedToken: (state, action) => {
      state.decodedToken = action.payload;
    },
    setChannelModules: (state, action) => {
      state.channelModules = action.payload;
    },
    // login actions
    setCredentials: (state, action) => {
      state.userInfo = action.payload;
      localStorage.setItem("userInfo", JSON.stringify(action.payload));
    },
    setLoginErrorMessage: (state, action) => {
      state.loginErrorMessage = action.payload;
    }
  }
});
var {
  setIsLoadingLogin,
  setDecodedToken,
  setIsLoginSuccess,
  setIsLoginError,
  setChannelModules,
  setLoginErrorMessage,
  // login actions
  setCredentials
} = authSlice.actions;
var auth_default = authSlice.reducer;

// src/reducers/navigation.ts
import { createSlice as createSlice2 } from "@reduxjs/toolkit";
var initialState2 = {
  switchToCustomerDetails: {
    customer: null,
    open: false,
    isPendingCustomer: false
  },
  switchToUserDetails: {
    open: false,
    user: null
  },
  switchToRoleDetails: {
    open: false,
    role: null,
    type: ""
  },
  isSidebarCollapsed: false,
  documentToggle: {
    open: false,
    imageUrl: ""
  }
};
var navigationSlice = createSlice2({
  name: "navigation",
  initialState: initialState2,
  reducers: {
    setSwitchToUserDetails: (state, action) => {
      state.switchToUserDetails = action.payload;
    },
    setSwitchToCustomerDetails: (state, action) => {
      state.switchToUserDetails = action.payload;
      state.switchToRoleDetails = {
        open: false,
        role: null,
        type: ""
      };
    },
    setSwitchToRoleDetails: (state, action) => {
      state.switchToRoleDetails = action.payload;
      state.switchToUserDetails = {
        open: false,
        user: null
      };
    },
    setSidebarCollapsed: (state, action) => {
      state.isSidebarCollapsed = action.payload;
    },
    setDocumentToggle: (state, action) => {
      state.documentToggle = action.payload;
    }
  }
});
var {
  setSwitchToUserDetails,
  setSwitchToCustomerDetails,
  setSwitchToRoleDetails,
  setSidebarCollapsed,
  setDocumentToggle
} = navigationSlice.actions;
var navigation_default = navigationSlice.reducer;

// src/reducers/notifications.ts
import { createSlice as createSlice3 } from "@reduxjs/toolkit";
var initialLocalNotification = {
  message: "",
  type: "info"
};
var initialState3 = {
  localNotification: initialLocalNotification.message,
  localNotificationType: initialLocalNotification.type
};
var notificationsSlice = createSlice3({
  name: "notifications",
  initialState: initialState3,
  reducers: {
    setNotification: (state, action) => {
      state.localNotification = action.payload.message;
      state.localNotificationType = action.payload.type;
    },
    clearNotification: () => initialState3
  }
});
var { setNotification, clearNotification } = notificationsSlice.actions;
var notifications_default = notificationsSlice.reducer;

// src/reducers/overlays.ts
import { createSlice as createSlice4 } from "@reduxjs/toolkit";
var initialState4 = {
  openUserChangeLogDrawer: false,
  drawer: {
    drawerChildren: null,
    header: "",
    open: false
  }
};
var overlaySlice = createSlice4({
  name: "overlays",
  initialState: initialState4,
  reducers: {
    setOpenUserChangeLogs: (state, action) => {
      state.openUserChangeLogDrawer = action.payload;
    },
    setDrawer: (state, action) => {
      state.drawer = action.payload;
    },
    resetDrawer: (state) => {
      state.drawer = initialState4.drawer;
    }
  }
});
var { setOpenUserChangeLogs, setDrawer, resetDrawer } = overlaySlice.actions;
var overlays_default = overlaySlice.reducer;

// src/reducers/platform.ts
import { createSlice as createSlice5 } from "@reduxjs/toolkit";
var initialState5 = {
  platforms: [],
  isLoadingPlatforms: false,
  platformsSummary: {
    pageNumber: 0,
    pageSize: 0,
    totalNumberOfPages: 0,
    totalElements: 0
  }
};
var platformSlice = createSlice5({
  name: "platform",
  initialState: initialState5,
  reducers: {
    setPlatforms: (state, action) => {
      state.platforms = action.payload;
    },
    setIsLoadingPlatforms: (state, action) => {
      state.isLoadingPlatforms = action.payload;
    },
    setPlatformsSummary: (state, action) => {
      state.platformsSummary = action.payload;
    }
  }
});
var {
  setPlatforms,
  setIsLoadingPlatforms,
  setPlatformsSummary
} = platformSlice.actions;
var platform_default = platformSlice.reducer;

// src/reducers/rootReducer.ts
import { combineReducers } from "redux";
var rootReducer = combineReducers({
  navigation: navigation_default,
  notifications: notifications_default,
  auth: auth_default,
  overlay: overlays_default,
  platform: platform_default
});

export {
  setIsLoadingLogin,
  setDecodedToken,
  setIsLoginSuccess,
  setIsLoginError,
  setChannelModules,
  setLoginErrorMessage,
  setCredentials,
  auth_default,
  setSwitchToUserDetails,
  setSwitchToCustomerDetails,
  setSwitchToRoleDetails,
  setSidebarCollapsed,
  setDocumentToggle,
  navigation_default,
  setNotification,
  clearNotification,
  notifications_default,
  setOpenUserChangeLogs,
  setDrawer,
  resetDrawer,
  overlays_default,
  setPlatforms,
  setIsLoadingPlatforms,
  setPlatformsSummary,
  platform_default,
  rootReducer
};
