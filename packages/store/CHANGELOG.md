# @dtbx/store

## 0.1.1

### Patch Changes

- d70f1b7: - Add platforms reducer to global store store package
  - Add platform actions to global store package

## 0.1.0

### Minor Changes

- Remove NextJS dependency
- refactor auth action `handleLogin` to remove dependency on the Next router
- Update package.json to remove `peerDependencies` `"next": "15.2.3" `
- Bump up `axios` version from `1.8.4` to `1.11.0`

## 0.0.3

### Patch Changes

- Update store imports
  Removed useAppSelector, useAppDispatch from the packages

## 0.0.2

### Patch Changes

- Bug fixes and perfomance improvements

## 0.0.1

### Patch Changes

- Initial version setup
