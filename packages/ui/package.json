{"name": "@dtbx/ui", "version": "0.1.0", "type": "module", "sideEffects": false, "license": "UNLICENSED", "types": "./dist/types.d.ts", "exports": {"./types": {"types": "./dist/types.d.ts"}, "./theme/*.css": "./dist/theme/*.css", "./theme": {"types": "./dist/theme/index.d.ts", "default": "./dist/theme/index.js"}, "./hooks": {"types": "./dist/hooks/index.d.ts", "default": "./dist/hooks/index.js"}, "./components": {"types": "./dist/components/index.d.ts", "default": "./dist/components/index.js"}, "./components/*": {"types": "./dist/components/*/index.d.ts", "default": "./dist/components/*/index.js"}, "./icons": {"types": "./dist/components/SvgIcons/index.d.ts", "default": "./dist/components/SvgIcons/index.js"}}, "files": ["dist/**"], "publishConfig": {"access": "restricted"}, "scripts": {"compile": "tsup", "lint": "eslint . --max-warnings 0", "clean": "rimraf .turbo __tests__/coverage"}, "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/base": "5.0.0-beta.70", "@mui/utils": "^6.1.10", "@mui/x-date-pickers": "^7.23.1", "@reduxjs/toolkit": "^2.4.0", "dayjs": "^1.11.13", "formik": "^2.4.6", "mui-tel-input": "^8.0.1", "prop-types": "15.8.1", "simplebar-react": "3.3.0", "tiny-case": "^1.0.3", "yup": "^1.5.0"}, "peerDependencies": {"@mui/icons-material": "^6.0.0", "@mui/material": "^6.0.0", "next": ">=15.0.0", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@dtbx/eslint-config": "workspace:*", "@dtbx/typescript-config": "workspace:*", "@types/eslint": "^9.6.1", "@types/node": "^20.17.27", "@types/prop-types": "^15.7.14", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "dotenv": "^16.4.5", "eslint": "^9.23.0", "rimraf": "^6.0.1", "tsup": "^8.5.0", "typescript": "^5.8.2"}}