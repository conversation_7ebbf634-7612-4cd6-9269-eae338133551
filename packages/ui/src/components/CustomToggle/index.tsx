'use client'
import  { JSX, FC } from 'react'
import { styled } from '@mui/material/styles'
import { TabsList as BaseTabsList, TabsListOwnProps } from '@mui/base/TabsList'
import { TabPanel as BaseTabPanel, TabPanelOwnProps } from '@mui/base/TabPanel'

export interface ITableData {
  id: string
  event: string
  eventSource?: string
  eventDate: string
  maker?: string
  makerTimestamp?: string
  checker?: string
  checkerTimestamp?: string
}
interface CustomToggleProps {
  data: ITableData[]
  renderTable: (filteredData: ITableData[]) => JSX.Element
  onChange?: (value: number) => void
}

const grey = {
  50: '#F3F6F9',
  100: '#E5EAF2',
  200: '#DAE2ED',
  300: '#C7D0DD',
  400: '#B0B8C4',
  500: '#9DA8B7',
  600: '#6B7A90',
  700: '#434D5B',
  800: '#303740',
  900: '#1C2025',
  950: '#D0D5DD',
  951: '#EAECF0',
}

export const TabPanel: FC<TabPanelOwnProps> = styled(BaseTabPanel)(
  () => `
  width: 100%;
  font-family: 'IBM Plex Sans', sans-serif;
  font-size: 0.875rem;
  background: ${grey[900]};
  border: 1px solid grey[200];
  border-radius: 12px;
  opacity: 0.6;
  `
)

export const TabsList: FC<TabsListOwnProps> = styled(BaseTabsList)(
  () => `
  width: 28%;
  background-color: ${grey[951]};
  border: 2px solid ${grey[950]};
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  align-content: space-between;
  `
)
